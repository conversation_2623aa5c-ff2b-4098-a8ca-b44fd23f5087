# Meta-Reward Design for Safe Exploration in Reinforcement Learning

A comprehensive PyTorch-based implementation of meta-reward learning for safe exploration in reinforcement learning environments, featuring a user-friendly web interface built with Streamlit.

## 🎯 Project Overview

This project implements a novel approach to safe exploration in RL by combining:

- **PPO Base Learner**: Proximal Policy Optimization as the core RL algorithm
- **Meta-Learner**: Neural network that adaptively shapes rewards for safer exploration
- **Safety Critic**: Network that predicts and helps prevent safety violations
- **Safe CartPole Environment**: Modified CartPole with configurable unsafe regions

## 🚀 Key Features

✅ **Real-time Training Monitoring** - Watch your agent learn with live visualizations  
✅ **Interactive Web Interface** - User-friendly Streamlit dashboard  
✅ **Configurable Experiments** - Easy YAML-based configuration system  
✅ **Safety Metrics Tracking** - Comprehensive safety violation monitoring  
✅ **Model Comparison Tools** - Compare different approaches side-by-side  
✅ **Windows Compatible** - Fully tested on Windows systems  
✅ **PyTorch Native** - Pure PyTorch implementation for maximum flexibility  

## 📋 Requirements

- **Python 3.8+**
- **Windows 10/11** (primary target, but works on other platforms)
- **8GB RAM** (recommended)
- **GPU** (optional, CPU training supported)

## 🛠️ Installation

### Step 1: Clone the Repository
```bash
git clone <repository-url>
cd meta_reward_safe_rl
```

### Step 2: Create Virtual Environment (Recommended)
```bash
python -m venv venv
venv\Scripts\activate  # On Windows
# source venv/bin/activate  # On Linux/Mac
```

### Step 3: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 4: Verify Installation
```bash
python -c "import torch; import gymnasium; import streamlit; print('Installation successful!')"
```

## 🎮 Quick Start

### Option 1: Web Interface (Recommended)
```bash
python main.py --mode ui
```
This launches the Streamlit web interface at `http://localhost:8501`

### Option 2: Command Line Training
```bash
python main.py --mode train --config experiments/configs/base_cartpole.yml
```

## 📁 Project Structure

```
meta_reward_safe_rl/
├── main.py                    # Main entry point
├── requirements.txt           # Python dependencies
├── README.md                 # This file
│
├── agents/                   # RL Agents
│   ├── ppo_agent.py         # PPO implementation
│   ├── meta_learner.py      # Meta-reward learner
│   └── safety_critic.py     # Safety violation predictor
│
├── environments/             # RL Environments
│   └── cartpole_safe.py     # Safe CartPole environment
│
├── experiments/              # Training & Configs
│   ├── train.py             # Main training script
│   └── configs/             # Configuration files
│       ├── base_cartpole.yml
│       └── baseline_ppo.yml
│
├── utils/                    # Utilities
│   ├── logging.py           # Experiment logging
│   └── visualization.py     # Plotting tools
│
├── ui/                       # Web Interface
│   └── app.py               # Streamlit application
│
└── logs/                     # Training logs (created automatically)
```

## ⚙️ Configuration

Experiments are configured using YAML files in `experiments/configs/`. Here's an example:

```yaml
experiment:
  name: "meta_reward_safe_cartpole"
  seed: 42
  total_episodes: 2000
  max_steps_per_episode: 500

environment:
  name: "SafeCartPole"
  unsafe_angle_threshold: 20.0  # degrees
  unsafe_position_threshold: 1.5
  safety_penalty: -10.0

agent:
  type: "PPO"
  lr: 3e-4
  gamma: 0.99

meta_learner:
  enabled: true
  lr: 1e-3
  shaping_scale: 0.1

safety_critic:
  enabled: true
  lr: 1e-3
```

## 🧪 Running Experiments

### 1. Using the Web Interface

1. Launch the web interface: `python main.py --mode ui`
2. Navigate to the "🚀 Training" tab
3. Select a configuration or create a custom one
4. Click "▶️ Start Training"
5. Monitor progress in real-time

### 2. Using Command Line

```bash
# Run meta-reward experiment
python main.py --mode train --config experiments/configs/base_cartpole.yml

# Run baseline PPO for comparison
python main.py --mode train --config experiments/configs/baseline_ppo.yml
```

### 3. Custom Configurations

Create your own configuration file:

```bash
cp experiments/configs/base_cartpole.yml experiments/configs/my_experiment.yml
# Edit my_experiment.yml with your parameters
python main.py --mode train --config experiments/configs/my_experiment.yml
```

## 📊 Understanding the Results

### Key Metrics

- **Episode Reward**: Total reward achieved per episode
- **Safety Violations**: Number of constraint violations per episode
- **Violation Rate**: Percentage of steps that violate safety constraints
- **Meta-Reward Signal**: Adaptive reward shaping signal from meta-learner
- **Episode Length**: Number of steps before episode termination

### Safety Constraints in CartPole

The Safe CartPole environment defines unsafe regions as:

1. **Extreme Pole Angles**: Beyond ±20° (configurable)
2. **Extreme Cart Positions**: Beyond ±1.5 units (configurable)
3. **High Velocities**: Combined velocity > 3.0 units/s (configurable)

### Expected Results

A successful meta-reward approach should show:
- ✅ Reduced safety violations over time
- ✅ Maintained or improved task performance
- ✅ Adaptive meta-reward signal that responds to safety needs

## 🔧 Troubleshooting

### Common Issues

**1. Import Errors**
```bash
# Ensure all dependencies are installed
pip install -r requirements.txt

# Check Python path
python -c "import sys; print(sys.path)"
```

**2. CUDA Issues**
```bash
# Check CUDA availability
python -c "import torch; print(torch.cuda.is_available())"

# Force CPU usage if needed
# Edit config file: device: "cpu"
```

**3. Streamlit Port Issues**
```bash
# Use different port
streamlit run ui/app.py --server.port 8502
```

**4. Memory Issues**
- Reduce `batch_size` in configuration
- Reduce `trajectory_length` in agent config
- Close other applications

### Windows-Specific Notes

- Use PowerShell or Command Prompt (not Git Bash for some operations)
- Ensure Python is added to PATH
- If you encounter permission issues, run as administrator

## 🧠 Algorithm Details

### Meta-Learner Architecture

The meta-learner is a neural network that:
1. Takes as input: current state, action, and safety violation history
2. Outputs a scalar reward shaping signal
3. Is trained to minimize safety violations while maintaining performance

### Safety Critic

The safety critic:
1. Predicts the probability of safety violations for state-action pairs
2. Uses supervised learning on actual violation labels
3. Helps guide the meta-learner's reward shaping

### Joint Training Process

1. **Environment Step**: Agent takes action, environment returns reward + safety signal
2. **Meta-Reward**: Meta-learner computes additional reward shaping
3. **Combined Reward**: Agent learns from environment reward + meta-reward
4. **Updates**: PPO, meta-learner, and safety critic are updated in sequence

## 📈 Extending the Project

### Adding New Environments

1. Create environment wrapper in `environments/`
2. Implement safety constraint checking
3. Update configuration options
4. Add to environment factory in `train.py`

### Adding New Algorithms

1. Implement agent in `agents/`
2. Follow the same interface as `PPOAgent`
3. Update training loop in `experiments/train.py`

### Custom Safety Constraints

1. Modify environment wrapper
2. Implement custom `_check_safety_violation()` method
3. Update safety metrics tracking

## 📚 References

- [Proximal Policy Optimization](https://arxiv.org/abs/1707.06347)
- [Safe Reinforcement Learning](https://arxiv.org/abs/1801.08757)
- [Reward Shaping](https://people.eecs.berkeley.edu/~pabbeel/cs287-fa09/readings/NgHaradaRussell-shaping-ICML1999.pdf)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenAI Gymnasium for the base CartPole environment
- PyTorch team for the deep learning framework
- Streamlit team for the web interface framework
