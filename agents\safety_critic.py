"""
Safety Critic for predicting safety violations in RL environments.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Any, Tuple
from collections import deque


class SafetyCriticNetwork(nn.Module):
    """Neural network for predicting safety costs/violations."""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 64):
        super(SafetyCriticNetwork, self).__init__()
        
        input_dim = state_dim + action_dim
        
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()  # Output probability of violation
        )
    
    def forward(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """Forward pass to predict safety violation probability."""
        x = torch.cat([state, action], dim=-1)
        return self.network(x)


class SafetyCritic:
    """Safety critic for predicting and learning from safety violations."""
    
    def __init__(self, state_dim: int, action_dim: int, lr: float = 1e-3,
                 gamma: float = 0.99, device: str = "cpu"):
        self.device = device
        self.gamma = gamma
        
        # Initialize network
        self.network = SafetyCriticNetwork(state_dim, action_dim).to(device)
        self.optimizer = optim.Adam(self.network.parameters(), lr=lr)
        
        # Experience buffer for safety learning
        self.reset_storage()
        
    def reset_storage(self):
        """Reset experience storage."""
        self.states = []
        self.actions = []
        self.safety_costs = []
        self.next_states = []
        self.next_actions = []
        self.dones = []
        
    def store_transition(self, state: np.ndarray, action: np.ndarray,
                        safety_cost: float, next_state: np.ndarray,
                        next_action: np.ndarray, done: bool):
        """Store a safety transition."""
        self.states.append(state)
        self.actions.append(action)
        self.safety_costs.append(safety_cost)
        self.next_states.append(next_state)
        self.next_actions.append(next_action)
        self.dones.append(done)
    
    def predict_safety_cost(self, state: np.ndarray, action: np.ndarray) -> float:
        """Predict safety cost for given state-action pair."""
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        action_tensor = torch.FloatTensor(action).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            safety_prob = self.network(state_tensor, action_tensor)
            
        return safety_prob.item()
    
    def get_safety_signal(self, state: np.ndarray, action: np.ndarray,
                         threshold: float = 0.5) -> float:
        """Get binary safety signal based on predicted probability."""
        prob = self.predict_safety_cost(state, action)
        return 1.0 if prob > threshold else 0.0
    
    def update(self, batch_size: int = 32) -> Dict[str, float]:
        """Update safety critic using stored experiences."""
        if len(self.states) < batch_size:
            return {}
        
        # Sample batch
        indices = np.random.choice(len(self.states), batch_size, replace=False)
        
        batch_states = torch.FloatTensor([self.states[i] for i in indices]).to(self.device)
        batch_actions = torch.FloatTensor([self.actions[i] for i in indices]).to(self.device)
        batch_costs = torch.FloatTensor([self.safety_costs[i] for i in indices]).to(self.device)
        batch_next_states = torch.FloatTensor([self.next_states[i] for i in indices]).to(self.device)
        batch_next_actions = torch.FloatTensor([self.next_actions[i] for i in indices]).to(self.device)
        batch_dones = torch.FloatTensor([self.dones[i] for i in indices]).to(self.device)
        
        # Current safety predictions
        current_safety_preds = self.network(batch_states, batch_actions).squeeze()
        
        # Target safety values using TD learning
        with torch.no_grad():
            next_safety_preds = self.network(batch_next_states, batch_next_actions).squeeze()
            targets = batch_costs + self.gamma * next_safety_preds * (1 - batch_dones)
        
        # Compute loss
        loss = F.mse_loss(current_safety_preds, targets)
        
        # Update
        self.optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.network.parameters(), 1.0)
        self.optimizer.step()
        
        return {
            'safety_critic_loss': loss.item(),
            'avg_safety_pred': current_safety_preds.mean().item(),
            'avg_safety_target': targets.mean().item()
        }
    
    def update_supervised(self, batch_size: int = 32) -> Dict[str, float]:
        """Update safety critic using supervised learning on violation labels."""
        if len(self.states) < batch_size:
            return {}
        
        # Sample batch
        indices = np.random.choice(len(self.states), batch_size, replace=False)
        
        batch_states = torch.FloatTensor([self.states[i] for i in indices]).to(self.device)
        batch_actions = torch.FloatTensor([self.actions[i] for i in indices]).to(self.device)
        batch_labels = torch.FloatTensor([self.safety_costs[i] for i in indices]).to(self.device)
        
        # Predictions
        predictions = self.network(batch_states, batch_actions).squeeze()
        
        # Binary cross-entropy loss for violation prediction
        loss = F.binary_cross_entropy(predictions, batch_labels)
        
        # Update
        self.optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.network.parameters(), 1.0)
        self.optimizer.step()
        
        # Compute accuracy
        predicted_labels = (predictions > 0.5).float()
        accuracy = (predicted_labels == batch_labels).float().mean()
        
        return {
            'safety_critic_loss': loss.item(),
            'safety_accuracy': accuracy.item(),
            'avg_safety_pred': predictions.mean().item()
        }
    
    def clear_old_experiences(self, keep_ratio: float = 0.8):
        """Clear old experiences to prevent memory issues."""
        if len(self.states) > 10000:  # Keep buffer manageable
            keep_count = int(len(self.states) * keep_ratio)
            self.states = self.states[-keep_count:]
            self.actions = self.actions[-keep_count:]
            self.safety_costs = self.safety_costs[-keep_count:]
            self.next_states = self.next_states[-keep_count:]
            self.next_actions = self.next_actions[-keep_count:]
            self.dones = self.dones[-keep_count:]
    
    def save(self, filepath: str):
        """Save the safety critic model."""
        torch.save({
            'network_state_dict': self.network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict()
        }, filepath)
    
    def load(self, filepath: str):
        """Load the safety critic model."""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.network.load_state_dict(checkpoint['network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
