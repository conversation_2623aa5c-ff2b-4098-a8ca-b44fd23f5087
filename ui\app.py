"""
Streamlit web interface for Meta-Reward Safe RL experiments.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import yaml
import os
import sys
import threading
import time
from pathlib import Path
import subprocess
import json

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from experiments.train import SafeRLTrainer
from utils.logging import load_experiment_data
from utils.visualization import ExperimentVisualizer


class StreamlitApp:
    """Main Streamlit application for Meta-Reward Safe RL."""
    
    def __init__(self):
        self.setup_page()
        self.training_thread = None
        self.trainer = None
        
    def setup_page(self):
        """Setup Streamlit page configuration."""
        st.set_page_config(
            page_title="Meta-Reward Safe RL",
            page_icon="🤖",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        st.title("🤖 Meta-Reward Design for Safe Exploration in RL")
        st.markdown("---")
    
    def run(self):
        """Main application runner."""
        # Sidebar for navigation
        page = st.sidebar.selectbox(
            "Navigation",
            ["🏠 Home", "🚀 Training", "📊 Results", "⚙️ Configuration", "📈 Live Monitoring"]
        )
        
        if page == "🏠 Home":
            self.show_home()
        elif page == "🚀 Training":
            self.show_training()
        elif page == "📊 Results":
            self.show_results()
        elif page == "⚙️ Configuration":
            self.show_configuration()
        elif page == "📈 Live Monitoring":
            self.show_live_monitoring()
    
    def show_home(self):
        """Show home page with project overview."""
        st.header("Welcome to Meta-Reward Safe RL")
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.markdown("""
            ### Project Overview
            
            This application implements a **Meta-Reward Design system for Safe Exploration** 
            in Reinforcement Learning. The system combines:
            
            - **PPO Base Learner**: Proximal Policy Optimization agent
            - **Meta-Learner**: Neural network for adaptive reward shaping
            - **Safety Critic**: Network to predict and prevent safety violations
            - **Safe CartPole Environment**: Modified CartPole with unsafe regions
            
            ### Key Features
            
            ✅ **Real-time Training Monitoring**  
            ✅ **Interactive Visualizations**  
            ✅ **Configurable Experiments**  
            ✅ **Safety Metrics Tracking**  
            ✅ **Model Comparison Tools**  
            
            ### Getting Started
            
            1. **Configure** your experiment parameters
            2. **Start Training** with your chosen settings
            3. **Monitor** progress in real-time
            4. **Analyze** results with interactive plots
            """)
        
        with col2:
            st.info("""
            **Quick Stats**
            
            🎯 **Environment**: Safe CartPole  
            🧠 **Algorithm**: PPO + Meta-Learning  
            🛡️ **Safety**: Constraint-based  
            📊 **Visualization**: Real-time plots  
            """)
            
            # System status
            st.subheader("System Status")
            
            # Check if training is running
            if 'training_active' in st.session_state and st.session_state.training_active:
                st.success("🟢 Training Active")
            else:
                st.info("🔵 Ready to Train")
            
            # Check available configurations
            config_dir = project_root / "experiments" / "configs"
            if config_dir.exists():
                configs = list(config_dir.glob("*.yml"))
                st.metric("Available Configs", len(configs))
            
            # Check log directory
            log_dir = project_root / "logs"
            if log_dir.exists():
                experiments = list(log_dir.glob("*"))
                st.metric("Past Experiments", len(experiments))
    
    def show_training(self):
        """Show training interface."""
        st.header("🚀 Training Interface")
        
        col1, col2 = st.columns([1, 1])
        
        with col1:
            st.subheader("Experiment Configuration")
            
            # Configuration selection
            config_dir = project_root / "experiments" / "configs"
            config_files = list(config_dir.glob("*.yml")) if config_dir.exists() else []
            config_names = [f.stem for f in config_files]
            
            selected_config = st.selectbox(
                "Select Configuration",
                config_names,
                index=0 if config_names else None
            )
            
            if selected_config:
                config_path = config_dir / f"{selected_config}.yml"
                
                # Load and display config
                with open(config_path, 'r') as f:
                    config = yaml.safe_load(f)
                
                st.json(config)
                
                # Training controls
                st.subheader("Training Controls")
                
                col_start, col_stop = st.columns(2)
                
                with col_start:
                    if st.button("▶️ Start Training", type="primary"):
                        self.start_training(str(config_path))
                
                with col_stop:
                    if st.button("⏹️ Stop Training", type="secondary"):
                        self.stop_training()
        
        with col2:
            st.subheader("Training Status")
            
            # Training status
            if 'training_active' in st.session_state and st.session_state.training_active:
                st.success("🟢 Training in Progress")
                
                # Progress placeholder
                progress_placeholder = st.empty()
                status_placeholder = st.empty()
                
                # Show training progress if available
                self.show_training_progress(progress_placeholder, status_placeholder)
                
            else:
                st.info("🔵 No active training")
                
                # Show recent training logs
                self.show_recent_logs()
    
    def show_results(self):
        """Show results analysis interface."""
        st.header("📊 Results Analysis")
        
        # Experiment selection
        log_dir = project_root / "logs"
        if not log_dir.exists():
            st.warning("No experiment logs found. Run some training first!")
            return
        
        experiments = [d for d in log_dir.iterdir() if d.is_dir()]
        if not experiments:
            st.warning("No completed experiments found.")
            return
        
        experiment_names = [exp.name for exp in experiments]
        selected_experiment = st.selectbox("Select Experiment", experiment_names)
        
        if selected_experiment:
            experiment_path = log_dir / selected_experiment
            
            # Load experiment data
            try:
                data = load_experiment_data(str(experiment_path))
                
                if 'metrics' in data:
                    self.show_experiment_analysis(data['metrics'], selected_experiment)
                else:
                    st.error("No metrics data found in experiment.")
                    
            except Exception as e:
                st.error(f"Error loading experiment data: {e}")
    
    def show_configuration(self):
        """Show configuration editor."""
        st.header("⚙️ Configuration Editor")
        
        # Configuration templates
        st.subheader("Configuration Templates")
        
        templates = {
            "Meta-Reward Safe RL": {
                "experiment": {
                    "name": "custom_experiment",
                    "seed": 42,
                    "total_episodes": 1000,
                    "max_steps_per_episode": 500
                },
                "environment": {
                    "name": "SafeCartPole",
                    "unsafe_angle_threshold": 20.0,
                    "unsafe_position_threshold": 1.5,
                    "safety_penalty": -10.0
                },
                "agent": {
                    "type": "PPO",
                    "lr": 3e-4,
                    "gamma": 0.99
                },
                "meta_learner": {
                    "enabled": True,
                    "lr": 1e-3,
                    "shaping_scale": 0.1
                },
                "safety_critic": {
                    "enabled": True,
                    "lr": 1e-3
                }
            },
            "Baseline PPO": {
                "experiment": {
                    "name": "baseline_ppo",
                    "seed": 42,
                    "total_episodes": 1000,
                    "max_steps_per_episode": 500
                },
                "environment": {
                    "name": "SafeCartPole",
                    "unsafe_angle_threshold": 20.0,
                    "unsafe_position_threshold": 1.5,
                    "safety_penalty": -10.0
                },
                "agent": {
                    "type": "PPO",
                    "lr": 3e-4,
                    "gamma": 0.99
                },
                "meta_learner": {
                    "enabled": False
                },
                "safety_critic": {
                    "enabled": False
                }
            }
        }
        
        selected_template = st.selectbox("Select Template", list(templates.keys()))
        
        # Configuration editor
        st.subheader("Edit Configuration")
        
        config_editor = st.text_area(
            "Configuration (YAML)",
            value=yaml.dump(templates[selected_template], default_flow_style=False),
            height=400
        )
        
        # Save configuration
        col1, col2 = st.columns(2)
        
        with col1:
            config_name = st.text_input("Configuration Name", value="custom_config")
        
        with col2:
            if st.button("💾 Save Configuration"):
                try:
                    # Parse YAML
                    config = yaml.safe_load(config_editor)
                    
                    # Save to file
                    config_dir = project_root / "experiments" / "configs"
                    config_dir.mkdir(parents=True, exist_ok=True)
                    
                    config_path = config_dir / f"{config_name}.yml"
                    with open(config_path, 'w') as f:
                        yaml.dump(config, f, default_flow_style=False)
                    
                    st.success(f"Configuration saved as {config_name}.yml")
                    
                except Exception as e:
                    st.error(f"Error saving configuration: {e}")
    
    def show_live_monitoring(self):
        """Show live training monitoring."""
        st.header("📈 Live Training Monitoring")
        
        if not ('training_active' in st.session_state and st.session_state.training_active):
            st.info("No active training to monitor. Start training first!")
            return
        
        # Auto-refresh
        auto_refresh = st.checkbox("Auto-refresh (5s)", value=True)
        
        if auto_refresh:
            time.sleep(5)
            st.experimental_rerun()
        
        # Live plots placeholder
        self.show_live_plots()
    
    def start_training(self, config_path: str):
        """Start training in a separate thread."""
        if 'training_active' in st.session_state and st.session_state.training_active:
            st.warning("Training is already active!")
            return
        
        st.session_state.training_active = True
        st.session_state.training_config = config_path
        
        # Start training thread
        self.training_thread = threading.Thread(
            target=self._run_training_thread,
            args=(config_path,)
        )
        self.training_thread.daemon = True
        self.training_thread.start()
        
        st.success("Training started!")
    
    def stop_training(self):
        """Stop active training."""
        if 'training_active' in st.session_state:
            st.session_state.training_active = False
        
        st.info("Training stopped!")
    
    def _run_training_thread(self, config_path: str):
        """Run training in separate thread."""
        try:
            from experiments.train import run_training
            run_training(config_path)
        except Exception as e:
            st.error(f"Training error: {e}")
        finally:
            if 'training_active' in st.session_state:
                st.session_state.training_active = False

    def show_training_progress(self, progress_placeholder, status_placeholder):
        """Show training progress if available."""
        # This would be implemented with real-time monitoring
        # For now, show placeholder
        with progress_placeholder.container():
            st.progress(0.5)
            st.text("Episode 500/1000")

        with status_placeholder.container():
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Avg Reward", "150.2", "↑12.3")
            with col2:
                st.metric("Safety Violations", "3", "↓2")
            with col3:
                st.metric("Episode Length", "245", "↑15")

    def show_recent_logs(self):
        """Show recent training logs."""
        st.subheader("Recent Experiments")

        log_dir = project_root / "logs"
        if log_dir.exists():
            experiments = sorted([d for d in log_dir.iterdir() if d.is_dir()],
                               key=lambda x: x.stat().st_mtime, reverse=True)[:5]

            if experiments:
                for exp in experiments:
                    with st.expander(f"📁 {exp.name}"):
                        # Try to load basic info
                        config_file = exp / "config.json"
                        if config_file.exists():
                            with open(config_file, 'r') as f:
                                config = json.load(f)
                            st.json(config)
                        else:
                            st.text("Configuration not available")
            else:
                st.info("No recent experiments found.")
        else:
            st.info("No experiments directory found.")

    def show_experiment_analysis(self, metrics: dict, experiment_name: str):
        """Show detailed experiment analysis."""
        st.subheader(f"Analysis: {experiment_name}")

        # Summary metrics
        if metrics.get('reward'):
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                avg_reward = np.mean(metrics['reward'])
                st.metric("Average Reward", f"{avg_reward:.2f}")

            with col2:
                total_violations = sum(metrics.get('safety_violations', []))
                st.metric("Total Violations", total_violations)

            with col3:
                final_violation_rate = metrics.get('violation_rate', [0])[-1] if metrics.get('violation_rate') else 0
                st.metric("Final Violation Rate", f"{final_violation_rate:.3f}")

            with col4:
                total_episodes = len(metrics['reward'])
                st.metric("Total Episodes", total_episodes)

        # Interactive plots
        st.subheader("Interactive Plots")

        # Create tabs for different plot types
        tab1, tab2, tab3 = st.tabs(["📈 Learning Curves", "🛡️ Safety Analysis", "🔧 Training Metrics"])

        with tab1:
            self.plot_learning_curves_interactive(metrics)

        with tab2:
            self.plot_safety_analysis_interactive(metrics)

        with tab3:
            self.plot_training_metrics_interactive(metrics)

    def plot_learning_curves_interactive(self, metrics: dict):
        """Create interactive learning curves."""
        if not metrics.get('episode'):
            st.warning("No episode data available.")
            return

        episodes = metrics['episode']

        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Episode Rewards', 'Safety Violations', 'Episode Length', 'Violation Rate'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )

        # Episode rewards
        if metrics.get('reward'):
            fig.add_trace(
                go.Scatter(x=episodes, y=metrics['reward'], name='Reward',
                          line=dict(color='blue'), mode='lines'),
                row=1, col=1
            )

        # Safety violations
        if metrics.get('safety_violations'):
            fig.add_trace(
                go.Scatter(x=episodes, y=metrics['safety_violations'], name='Violations',
                          line=dict(color='red'), mode='lines'),
                row=1, col=2
            )

        # Episode length
        if metrics.get('episode_length'):
            fig.add_trace(
                go.Scatter(x=episodes, y=metrics['episode_length'], name='Length',
                          line=dict(color='green'), mode='lines'),
                row=2, col=1
            )

        # Violation rate
        if metrics.get('violation_rate'):
            fig.add_trace(
                go.Scatter(x=episodes, y=metrics['violation_rate'], name='Rate',
                          line=dict(color='orange'), mode='lines'),
                row=2, col=2
            )

        fig.update_layout(height=600, showlegend=False, title_text="Learning Curves")
        st.plotly_chart(fig, use_container_width=True)

    def plot_safety_analysis_interactive(self, metrics: dict):
        """Create interactive safety analysis plots."""
        if not metrics.get('episode'):
            st.warning("No episode data available.")
            return

        episodes = metrics['episode']

        # Safety vs Performance scatter plot
        if metrics.get('reward') and metrics.get('safety_violations'):
            fig_scatter = go.Figure()
            fig_scatter.add_trace(
                go.Scatter(
                    x=metrics['safety_violations'],
                    y=metrics['reward'],
                    mode='markers',
                    marker=dict(
                        size=8,
                        color=episodes,
                        colorscale='Viridis',
                        showscale=True,
                        colorbar=dict(title="Episode")
                    ),
                    text=[f"Episode {ep}" for ep in episodes],
                    hovertemplate="<b>Episode %{text}</b><br>" +
                                  "Violations: %{x}<br>" +
                                  "Reward: %{y:.2f}<extra></extra>"
                )
            )
            fig_scatter.update_layout(
                title="Safety vs Performance Trade-off",
                xaxis_title="Safety Violations",
                yaxis_title="Episode Reward",
                height=400
            )
            st.plotly_chart(fig_scatter, use_container_width=True)

        # Cumulative violations
        if metrics.get('safety_violations'):
            cumulative_violations = np.cumsum(metrics['safety_violations'])
            fig_cumulative = go.Figure()
            fig_cumulative.add_trace(
                go.Scatter(x=episodes, y=cumulative_violations,
                          name='Cumulative Violations', line=dict(color='red'))
            )
            fig_cumulative.update_layout(
                title="Cumulative Safety Violations",
                xaxis_title="Episode",
                yaxis_title="Total Violations",
                height=400
            )
            st.plotly_chart(fig_cumulative, use_container_width=True)

    def plot_training_metrics_interactive(self, metrics: dict):
        """Create interactive training metrics plots."""
        if not metrics.get('episode'):
            st.warning("No episode data available.")
            return

        episodes = metrics['episode']

        # Training losses
        loss_metrics = ['policy_loss', 'value_loss', 'meta_loss', 'safety_critic_loss']
        available_losses = [loss for loss in loss_metrics if metrics.get(loss)]

        if available_losses:
            fig_losses = go.Figure()

            colors = ['blue', 'red', 'green', 'orange']
            for i, loss_type in enumerate(available_losses):
                # Filter out zero values
                loss_data = [x for x in metrics[loss_type] if x != 0.0]
                if loss_data:
                    loss_episodes = episodes[:len(loss_data)]
                    fig_losses.add_trace(
                        go.Scatter(x=loss_episodes, y=loss_data,
                                  name=loss_type.replace('_', ' ').title(),
                                  line=dict(color=colors[i % len(colors)]))
                    )

            fig_losses.update_layout(
                title="Training Losses",
                xaxis_title="Episode",
                yaxis_title="Loss",
                height=400
            )
            st.plotly_chart(fig_losses, use_container_width=True)

        # Meta-reward signal
        if metrics.get('meta_reward'):
            fig_meta = go.Figure()
            fig_meta.add_trace(
                go.Scatter(x=episodes, y=metrics['meta_reward'],
                          name='Meta-Reward', line=dict(color='purple'))
            )
            fig_meta.add_hline(y=0, line_dash="dash", line_color="black", opacity=0.5)
            fig_meta.update_layout(
                title="Meta-Reward Signal",
                xaxis_title="Episode",
                yaxis_title="Meta-Reward",
                height=400
            )
            st.plotly_chart(fig_meta, use_container_width=True)

    def show_live_plots(self):
        """Show live training plots."""
        # This would be implemented with real-time data fetching
        # For now, show placeholder
        st.info("Live plotting would be implemented here with real-time data updates.")

        # Placeholder plots
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=[1, 2, 3, 4], y=[10, 11, 12, 13], mode='lines+markers'))
        fig.update_layout(title="Live Training Progress", height=400)
        st.plotly_chart(fig, use_container_width=True)


def main():
    """Main function to run the Streamlit app."""
    app = StreamlitApp()
    app.run()


if __name__ == "__main__":
    main()
