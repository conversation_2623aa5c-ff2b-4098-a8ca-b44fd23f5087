"""
<PERSON><PERSON>-<PERSON><PERSON> for adaptive reward shaping in safe exploration.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from collections import deque


class MetaLearnerNetwork(nn.Module):
    """Neural network for meta-learning reward shaping."""
    
    def __init__(self, state_dim: int, action_dim: int, history_length: int = 10,
                 hidden_dim: int = 64, use_lstm: bool = True):
        super(MetaLearnerNetwork, self).__init__()
        self.history_length = history_length
        self.use_lstm = use_lstm
        
        # Input dimension: state + action + safety violation history
        input_dim = state_dim + action_dim + history_length
        
        if use_lstm:
            self.lstm = nn.LSTM(input_dim, hidden_dim, batch_first=True)
            self.output_layer = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.<PERSON>L<PERSON>(),
                nn.Linear(hidden_dim // 2, 1),
                nn.Tanh()  # Output between -1 and 1
            )
        else:
            self.network = nn.Sequential(
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Linear(hidden_dim // 2, 1),
                nn.Tanh()  # Output between -1 and 1
            )
    
    def forward(self, state: torch.Tensor, action: torch.Tensor, 
                safety_history: torch.Tensor) -> torch.Tensor:
        """Forward pass to compute reward shaping signal."""
        # Concatenate inputs
        x = torch.cat([state, action, safety_history], dim=-1)
        
        if self.use_lstm:
            if len(x.shape) == 2:
                x = x.unsqueeze(1)  # Add sequence dimension
            lstm_out, _ = self.lstm(x)
            output = self.output_layer(lstm_out[:, -1, :])  # Use last output
        else:
            output = self.network(x)
        
        return output


class MetaLearner:
    """Meta-learner for adaptive reward shaping."""
    
    def __init__(self, state_dim: int, action_dim: int, lr: float = 1e-3,
                 history_length: int = 10, shaping_scale: float = 0.1,
                 use_lstm: bool = True, device: str = "cpu"):
        self.device = device
        self.history_length = history_length
        self.shaping_scale = shaping_scale
        
        # Initialize network
        self.network = MetaLearnerNetwork(
            state_dim, action_dim, history_length, use_lstm=use_lstm
        ).to(device)
        self.optimizer = optim.Adam(self.network.parameters(), lr=lr)
        
        # Safety violation history buffer
        self.safety_history = deque(maxlen=history_length)
        
        # Storage for meta-learning updates
        self.reset_storage()
        
    def reset_storage(self):
        """Reset storage for meta-learning updates."""
        self.states = []
        self.actions = []
        self.safety_histories = []
        self.target_rewards = []
        
    def reset_safety_history(self):
        """Reset safety violation history."""
        self.safety_history.clear()
        # Initialize with zeros
        for _ in range(self.history_length):
            self.safety_history.append(0.0)
    
    def update_safety_history(self, violation: float):
        """Update safety violation history with new violation signal."""
        self.safety_history.append(float(violation))
    
    def get_shaping_reward(self, state: np.ndarray, action: np.ndarray) -> float:
        """Get reward shaping signal for current state-action pair."""
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)

        # Ensure action is properly shaped for concatenation
        if action.ndim == 0:  # Scalar action
            action = np.array([action])
        action_tensor = torch.FloatTensor(action).unsqueeze(0).to(self.device)
        safety_tensor = torch.FloatTensor(list(self.safety_history)).unsqueeze(0).to(self.device)

        with torch.no_grad():
            shaping_signal = self.network(state_tensor, action_tensor, safety_tensor)

        return self.shaping_scale * shaping_signal.item()
    
    def store_experience(self, state: np.ndarray, action: np.ndarray,
                        target_reward: float):
        """Store experience for meta-learning update."""
        self.states.append(state)

        # Ensure action is properly shaped
        if action.ndim == 0:  # Scalar action
            action = np.array([action])
        self.actions.append(action)
        self.safety_histories.append(list(self.safety_history))
        self.target_rewards.append(target_reward)
    
    def compute_target_rewards(self, episode_rewards: List[float], 
                             episode_violations: List[float],
                             safety_weight: float = 1.0) -> List[float]:
        """Compute target rewards for meta-learning based on episode outcomes."""
        target_rewards = []
        
        # Simple strategy: penalize actions that led to violations
        # and reward actions that maintained safety while achieving good performance
        total_violations = sum(episode_violations)
        avg_reward = np.mean(episode_rewards) if episode_rewards else 0.0
        
        for i, (reward, violation) in enumerate(zip(episode_rewards, episode_violations)):
            # Base target is the normalized reward
            target = reward / (abs(avg_reward) + 1e-8) if avg_reward != 0 else 0.0
            
            # Penalize violations more heavily
            if violation > 0:
                target -= safety_weight * violation
            
            # Bonus for maintaining safety in high-reward episodes
            if total_violations == 0 and avg_reward > 0:
                target += 0.1
                
            target_rewards.append(target)
        
        return target_rewards
    
    def update(self, episode_rewards: List[float], episode_violations: List[float],
               safety_weight: float = 1.0) -> Dict[str, float]:
        """Update meta-learner based on episode outcomes."""
        if len(self.states) == 0:
            return {}
        
        # Compute target rewards
        target_rewards = self.compute_target_rewards(
            episode_rewards, episode_violations, safety_weight
        )
        
        # Update stored target rewards
        if len(target_rewards) == len(self.target_rewards):
            self.target_rewards = target_rewards
        
        # Convert to tensors
        states = torch.FloatTensor(np.array(self.states)).to(self.device)
        actions = torch.FloatTensor(np.array(self.actions)).to(self.device)
        safety_histories = torch.FloatTensor(np.array(self.safety_histories)).to(self.device)
        targets = torch.FloatTensor(self.target_rewards).to(self.device)
        
        # Forward pass
        predicted_rewards = self.network(states, actions, safety_histories).squeeze()
        
        # Compute loss
        loss = F.mse_loss(predicted_rewards, targets)
        
        # Update
        self.optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.network.parameters(), 1.0)
        self.optimizer.step()
        
        # Clear storage
        self.reset_storage()
        
        return {
            'meta_loss': loss.item(),
            'avg_target_reward': targets.mean().item(),
            'avg_predicted_reward': predicted_rewards.mean().item()
        }
    
    def save(self, filepath: str):
        """Save the meta-learner model."""
        torch.save({
            'network_state_dict': self.network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'safety_history': list(self.safety_history)
        }, filepath)
    
    def load(self, filepath: str):
        """Load the meta-learner model."""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.network.load_state_dict(checkpoint['network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        # Restore safety history if available
        if 'safety_history' in checkpoint:
            self.safety_history.clear()
            self.safety_history.extend(checkpoint['safety_history'])
