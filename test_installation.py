#!/usr/bin/env python3
"""
Test script to verify Meta-Reward Safe RL installation.
"""

import sys
import traceback
from pathlib import Path

def test_imports():
    """Test all required imports."""
    print("Testing imports...")
    
    try:
        import torch
        print(f"✅ PyTorch {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        import gymnasium as gym
        print(f"✅ Gymnasium")
    except ImportError as e:
        print(f"❌ Gymnasium import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy {np.__version__}")
    except ImportError as e:
        print(f"❌ NumPy import failed: {e}")
        return False
    
    try:
        import matplotlib.pyplot as plt
        print(f"✅ Matplotlib")
    except ImportError as e:
        print(f"❌ Matplotlib import failed: {e}")
        return False
    
    try:
        import streamlit as st
        print(f"✅ Streamlit")
    except ImportError as e:
        print(f"❌ Streamlit import failed: {e}")
        return False
    
    try:
        import plotly.graph_objects as go
        print(f"✅ Plotly")
    except ImportError as e:
        print(f"❌ Plotly import failed: {e}")
        return False
    
    try:
        import yaml
        print(f"✅ PyYAML")
    except ImportError as e:
        print(f"❌ PyYAML import failed: {e}")
        return False
    
    return True

def test_project_structure():
    """Test project structure."""
    print("\nTesting project structure...")
    
    required_dirs = [
        "agents",
        "environments", 
        "experiments",
        "experiments/configs",
        "utils",
        "ui"
    ]
    
    required_files = [
        "main.py",
        "requirements.txt",
        "README.md",
        "agents/ppo_agent.py",
        "agents/meta_learner.py",
        "agents/safety_critic.py",
        "environments/cartpole_safe.py",
        "experiments/train.py",
        "experiments/configs/base_cartpole.yml",
        "utils/logging.py",
        "utils/visualization.py",
        "ui/app.py"
    ]
    
    all_good = True
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ Directory: {dir_path}")
        else:
            print(f"❌ Missing directory: {dir_path}")
            all_good = False
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ File: {file_path}")
        else:
            print(f"❌ Missing file: {file_path}")
            all_good = False
    
    return all_good

def test_environment():
    """Test the Safe CartPole environment."""
    print("\nTesting Safe CartPole environment...")
    
    try:
        from environments.cartpole_safe import make_safe_cartpole
        
        env = make_safe_cartpole()
        obs, info = env.reset()
        
        print(f"✅ Environment created successfully")
        print(f"✅ Observation space: {env.observation_space}")
        print(f"✅ Action space: {env.action_space}")
        print(f"✅ Initial observation shape: {obs.shape}")
        print(f"✅ Safety info available: {'safety_info' in info}")
        
        # Test a few steps
        for i in range(3):
            action = env.action_space.sample()
            obs, reward, terminated, truncated, info = env.step(action)
            print(f"✅ Step {i+1}: reward={reward:.2f}, violation={info.get('safety_violation', 0):.3f}")
            
            if terminated or truncated:
                break
        
        env.close()
        return True
        
    except Exception as e:
        print(f"❌ Environment test failed: {e}")
        traceback.print_exc()
        return False

def test_agents():
    """Test agent creation."""
    print("\nTesting agents...")
    
    try:
        from agents.ppo_agent import PPOAgent
        from agents.meta_learner import MetaLearner
        from agents.safety_critic import SafetyCritic
        
        # Test PPO agent
        ppo_agent = PPOAgent(state_dim=4, action_dim=2, device="cpu")
        print("✅ PPO Agent created successfully")
        
        # Test meta-learner
        meta_learner = MetaLearner(state_dim=4, action_dim=2, device="cpu")
        print("✅ Meta-Learner created successfully")
        
        # Test safety critic
        safety_critic = SafetyCritic(state_dim=4, action_dim=2, device="cpu")
        print("✅ Safety Critic created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent test failed: {e}")
        traceback.print_exc()
        return False

def test_configuration():
    """Test configuration loading."""
    print("\nTesting configuration...")
    
    try:
        import yaml
        
        config_path = Path("experiments/configs/base_cartpole.yml")
        if not config_path.exists():
            print(f"❌ Configuration file not found: {config_path}")
            return False
        
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        required_sections = ['experiment', 'environment', 'agent', 'meta_learner', 'safety_critic']
        
        for section in required_sections:
            if section in config:
                print(f"✅ Configuration section: {section}")
            else:
                print(f"❌ Missing configuration section: {section}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        traceback.print_exc()
        return False

def test_training_setup():
    """Test training setup without actually training."""
    print("\nTesting training setup...")
    
    try:
        from experiments.train import SafeRLTrainer
        
        config_path = "experiments/configs/base_cartpole.yml"
        trainer = SafeRLTrainer(config_path)
        
        print("✅ Trainer created successfully")
        print(f"✅ Environment: {type(trainer.env).__name__}")
        print(f"✅ Agent: {type(trainer.agent).__name__}")
        print(f"✅ Meta-learner enabled: {trainer.meta_learner is not None}")
        print(f"✅ Safety critic enabled: {trainer.safety_critic is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ Training setup test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🤖 Meta-Reward Safe RL Installation Test")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Project Structure Test", test_project_structure),
        ("Environment Test", test_environment),
        ("Agents Test", test_agents),
        ("Configuration Test", test_configuration),
        ("Training Setup Test", test_training_setup)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Installation is successful.")
        print("\nYou can now run:")
        print("  python main.py --mode ui")
        print("or")
        print("  python main.py --mode train --config experiments/configs/base_cartpole.yml")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the installation.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
