"""
Logging utilities for Meta-Reward Safe RL experiments.
"""

import os
import json
import pickle
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging
from pathlib import Path


class ExperimentLogger:
    """Comprehensive logging for RL experiments."""
    
    def __init__(self, experiment_name: str, log_dir: str = "logs", 
                 save_frequency: int = 100):
        self.experiment_name = experiment_name
        self.log_dir = Path(log_dir)
        self.save_frequency = save_frequency
        
        # Create experiment directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.experiment_dir = self.log_dir / f"{experiment_name}_{timestamp}"
        self.experiment_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize logging
        self._setup_logging()
        
        # Data storage
        self.metrics = {
            'episode': [],
            'step': [],
            'reward': [],
            'episode_length': [],
            'safety_violations': [],
            'violation_rate': [],
            'meta_reward': [],
            'safety_cost': [],
            'policy_loss': [],
            'value_loss': [],
            'meta_loss': [],
            'safety_critic_loss': []
        }
        
        self.episode_data = []
        self.step_count = 0
        self.episode_count = 0
        
    def _setup_logging(self):
        """Setup Python logging."""
        log_file = self.experiment_dir / "experiment.log"
        
        # Create logger
        self.logger = logging.getLogger(f"MetaRewardSafeRL_{self.experiment_name}")
        self.logger.setLevel(logging.INFO)
        
        # Create handlers
        file_handler = logging.FileHandler(log_file)
        console_handler = logging.StreamHandler()
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        self.logger.info(f"Experiment started: {self.experiment_name}")
        self.logger.info(f"Log directory: {self.experiment_dir}")
    
    def log_episode(self, episode_reward: float, episode_length: int,
                   safety_violations: int, violation_rate: float,
                   meta_reward: float = 0.0, safety_cost: float = 0.0,
                   additional_metrics: Optional[Dict[str, float]] = None):
        """Log episode-level metrics."""
        self.episode_count += 1
        
        # Store basic metrics
        self.metrics['episode'].append(self.episode_count)
        self.metrics['step'].append(self.step_count)
        self.metrics['reward'].append(episode_reward)
        self.metrics['episode_length'].append(episode_length)
        self.metrics['safety_violations'].append(safety_violations)
        self.metrics['violation_rate'].append(violation_rate)
        self.metrics['meta_reward'].append(meta_reward)
        self.metrics['safety_cost'].append(safety_cost)
        
        # Add additional metrics
        if additional_metrics:
            for key, value in additional_metrics.items():
                if key not in self.metrics:
                    self.metrics[key] = [0.0] * (self.episode_count - 1)
                self.metrics[key].append(value)
        
        # Log to console/file
        self.logger.info(
            f"Episode {self.episode_count}: "
            f"Reward={episode_reward:.2f}, "
            f"Length={episode_length}, "
            f"Violations={safety_violations}, "
            f"ViolationRate={violation_rate:.3f}, "
            f"MetaReward={meta_reward:.3f}"
        )
        
        # Save periodically
        if self.episode_count % self.save_frequency == 0:
            self.save_metrics()
    
    def log_training_metrics(self, policy_loss: float = 0.0, value_loss: float = 0.0,
                           meta_loss: float = 0.0, safety_critic_loss: float = 0.0):
        """Log training-specific metrics."""
        # Pad lists to match episode count
        while len(self.metrics['policy_loss']) < self.episode_count:
            self.metrics['policy_loss'].append(0.0)
            self.metrics['value_loss'].append(0.0)
            self.metrics['meta_loss'].append(0.0)
            self.metrics['safety_critic_loss'].append(0.0)
        
        # Update latest values
        if self.metrics['policy_loss']:
            self.metrics['policy_loss'][-1] = policy_loss
            self.metrics['value_loss'][-1] = value_loss
            self.metrics['meta_loss'][-1] = meta_loss
            self.metrics['safety_critic_loss'][-1] = safety_critic_loss
    
    def log_step(self, step_data: Dict[str, Any]):
        """Log step-level data."""
        self.step_count += 1
        step_data['step'] = self.step_count
        step_data['episode'] = self.episode_count
        self.episode_data.append(step_data)
    
    def save_metrics(self):
        """Save metrics to files."""
        # Ensure all metric lists have the same length
        max_length = max(len(v) for v in self.metrics.values() if isinstance(v, list))

        for key, values in self.metrics.items():
            if isinstance(values, list):
                while len(values) < max_length:
                    values.append(0.0)

        # Save as CSV
        df = pd.DataFrame(self.metrics)
        csv_path = self.experiment_dir / "metrics.csv"
        df.to_csv(csv_path, index=False)
        
        # Save as JSON (convert numpy types to native Python types)
        json_path = self.experiment_dir / "metrics.json"
        json_metrics = {}
        for key, values in self.metrics.items():
            if isinstance(values, list):
                json_metrics[key] = [float(v) if hasattr(v, 'item') else v for v in values]
            else:
                json_metrics[key] = values

        with open(json_path, 'w') as f:
            json.dump(json_metrics, f, indent=2)
        
        # Save step data if available
        if self.episode_data:
            step_df = pd.DataFrame(self.episode_data)
            step_csv_path = self.experiment_dir / "step_data.csv"
            step_df.to_csv(step_csv_path, index=False)
        
        self.logger.info(f"Metrics saved to {csv_path}")
    
    def save_config(self, config: Dict[str, Any]):
        """Save experiment configuration."""
        config_path = self.experiment_dir / "config.json"
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        self.logger.info(f"Configuration saved to {config_path}")
    
    def save_model(self, model_dict: Dict[str, Any], filename: str = "model.pkl"):
        """Save model state."""
        model_path = self.experiment_dir / filename
        with open(model_path, 'wb') as f:
            pickle.dump(model_dict, f)
        self.logger.info(f"Model saved to {model_path}")
    
    def get_metrics_summary(self) -> Dict[str, float]:
        """Get summary statistics of metrics."""
        if not self.metrics['reward']:
            return {}
        
        summary = {}
        
        # Recent performance (last 100 episodes)
        recent_episodes = min(100, len(self.metrics['reward']))
        recent_rewards = self.metrics['reward'][-recent_episodes:]
        recent_violations = self.metrics['safety_violations'][-recent_episodes:]
        
        summary.update({
            'total_episodes': self.episode_count,
            'total_steps': self.step_count,
            'avg_reward': np.mean(self.metrics['reward']),
            'recent_avg_reward': np.mean(recent_rewards),
            'best_reward': max(self.metrics['reward']),
            'total_violations': sum(self.metrics['safety_violations']),
            'recent_avg_violations': np.mean(recent_violations),
            'current_violation_rate': self.metrics['violation_rate'][-1] if self.metrics['violation_rate'] else 0.0
        })
        
        return summary
    
    def close(self):
        """Close logger and save final metrics."""
        self.save_metrics()
        summary = self.get_metrics_summary()
        
        self.logger.info("Experiment completed!")
        self.logger.info("Final Summary:")
        for key, value in summary.items():
            self.logger.info(f"  {key}: {value}")
        
        # Close handlers
        for handler in self.logger.handlers:
            handler.close()


def set_random_seeds(seed: int):
    """Set random seeds for reproducibility."""
    import random
    import torch
    
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    
    # For deterministic behavior (may impact performance)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def load_experiment_data(experiment_dir: str) -> Dict[str, Any]:
    """Load experiment data from directory."""
    experiment_path = Path(experiment_dir)
    
    data = {}
    
    # Load metrics
    metrics_path = experiment_path / "metrics.json"
    if metrics_path.exists():
        with open(metrics_path, 'r') as f:
            data['metrics'] = json.load(f)
    
    # Load config
    config_path = experiment_path / "config.json"
    if config_path.exists():
        with open(config_path, 'r') as f:
            data['config'] = json.load(f)
    
    # Load step data
    step_data_path = experiment_path / "step_data.csv"
    if step_data_path.exists():
        data['step_data'] = pd.read_csv(step_data_path)
    
    return data
