"""
Visualization utilities for Meta-Reward Safe RL experiments.
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Any, Optional, Tuple
import os


# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


class ExperimentVisualizer:
    """Visualization tools for RL experiments."""
    
    def __init__(self, save_dir: str = "plots"):
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
    
    def plot_learning_curves(self, metrics: Dict[str, List], 
                           window_size: int = 100,
                           save_name: str = "learning_curves.png") -> plt.Figure:
        """Plot learning curves with moving averages."""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Learning Curves', fontsize=16)
        
        episodes = metrics.get('episode', [])
        
        # Reward curve
        if 'reward' in metrics and metrics['reward']:
            rewards = metrics['reward']
            smoothed_rewards = self._moving_average(rewards, window_size)
            
            axes[0, 0].plot(episodes, rewards, alpha=0.3, color='blue', label='Raw')
            axes[0, 0].plot(episodes, smoothed_rewards, color='blue', linewidth=2, label=f'MA({window_size})')
            axes[0, 0].set_title('Episode Rewards')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('Reward')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
        
        # Safety violations
        if 'safety_violations' in metrics and metrics['safety_violations']:
            violations = metrics['safety_violations']
            smoothed_violations = self._moving_average(violations, window_size)
            
            axes[0, 1].plot(episodes, violations, alpha=0.3, color='red', label='Raw')
            axes[0, 1].plot(episodes, smoothed_violations, color='red', linewidth=2, label=f'MA({window_size})')
            axes[0, 1].set_title('Safety Violations per Episode')
            axes[0, 1].set_xlabel('Episode')
            axes[0, 1].set_ylabel('Violations')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
        
        # Episode length
        if 'episode_length' in metrics and metrics['episode_length']:
            lengths = metrics['episode_length']
            smoothed_lengths = self._moving_average(lengths, window_size)
            
            axes[1, 0].plot(episodes, lengths, alpha=0.3, color='green', label='Raw')
            axes[1, 0].plot(episodes, smoothed_lengths, color='green', linewidth=2, label=f'MA({window_size})')
            axes[1, 0].set_title('Episode Length')
            axes[1, 0].set_xlabel('Episode')
            axes[1, 0].set_ylabel('Steps')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
        
        # Violation rate
        if 'violation_rate' in metrics and metrics['violation_rate']:
            rates = metrics['violation_rate']
            smoothed_rates = self._moving_average(rates, window_size)
            
            axes[1, 1].plot(episodes, rates, alpha=0.3, color='orange', label='Raw')
            axes[1, 1].plot(episodes, smoothed_rates, color='orange', linewidth=2, label=f'MA({window_size})')
            axes[1, 1].set_title('Violation Rate')
            axes[1, 1].set_xlabel('Episode')
            axes[1, 1].set_ylabel('Rate')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save plot
        save_path = os.path.join(self.save_dir, save_name)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_training_losses(self, metrics: Dict[str, List],
                           save_name: str = "training_losses.png") -> plt.Figure:
        """Plot training losses."""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Training Losses', fontsize=16)
        
        episodes = metrics.get('episode', [])
        
        loss_types = [
            ('policy_loss', 'Policy Loss', 'blue'),
            ('value_loss', 'Value Loss', 'red'),
            ('meta_loss', 'Meta-Learner Loss', 'green'),
            ('safety_critic_loss', 'Safety Critic Loss', 'orange')
        ]
        
        for i, (loss_key, title, color) in enumerate(loss_types):
            row, col = i // 2, i % 2
            
            if loss_key in metrics and metrics[loss_key]:
                losses = [x for x in metrics[loss_key] if x != 0.0]  # Filter out zeros
                if losses:
                    loss_episodes = episodes[:len(losses)]
                    axes[row, col].plot(loss_episodes, losses, color=color, linewidth=2)
                    axes[row, col].set_title(title)
                    axes[row, col].set_xlabel('Episode')
                    axes[row, col].set_ylabel('Loss')
                    axes[row, col].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save plot
        save_path = os.path.join(self.save_dir, save_name)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_safety_analysis(self, metrics: Dict[str, List],
                           save_name: str = "safety_analysis.png") -> plt.Figure:
        """Plot detailed safety analysis."""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Safety Analysis', fontsize=16)
        
        episodes = metrics.get('episode', [])
        
        # Cumulative violations
        if 'safety_violations' in metrics:
            violations = metrics['safety_violations']
            cumulative_violations = np.cumsum(violations)
            
            axes[0, 0].plot(episodes, cumulative_violations, color='red', linewidth=2)
            axes[0, 0].set_title('Cumulative Safety Violations')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('Total Violations')
            axes[0, 0].grid(True, alpha=0.3)
        
        # Violation rate over time
        if 'violation_rate' in metrics:
            rates = metrics['violation_rate']
            axes[0, 1].plot(episodes, rates, color='orange', linewidth=2)
            axes[0, 1].axhline(y=0.1, color='red', linestyle='--', alpha=0.7, label='10% threshold')
            axes[0, 1].set_title('Violation Rate Over Time')
            axes[0, 1].set_xlabel('Episode')
            axes[0, 1].set_ylabel('Violation Rate')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
        
        # Safety vs Performance trade-off
        if 'reward' in metrics and 'safety_violations' in metrics:
            rewards = metrics['reward']
            violations = metrics['safety_violations']
            
            # Scatter plot
            axes[1, 0].scatter(violations, rewards, alpha=0.6, color='blue')
            axes[1, 0].set_title('Safety vs Performance Trade-off')
            axes[1, 0].set_xlabel('Safety Violations')
            axes[1, 0].set_ylabel('Episode Reward')
            axes[1, 0].grid(True, alpha=0.3)
        
        # Meta-reward signal
        if 'meta_reward' in metrics:
            meta_rewards = metrics['meta_reward']
            axes[1, 1].plot(episodes, meta_rewards, color='green', linewidth=2)
            axes[1, 1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
            axes[1, 1].set_title('Meta-Reward Signal')
            axes[1, 1].set_xlabel('Episode')
            axes[1, 1].set_ylabel('Meta-Reward')
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save plot
        save_path = os.path.join(self.save_dir, save_name)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def create_interactive_dashboard(self, metrics: Dict[str, List]) -> go.Figure:
        """Create interactive Plotly dashboard."""
        episodes = metrics.get('episode', [])
        
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Episode Rewards', 'Safety Violations', 
                          'Episode Length', 'Violation Rate'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # Rewards
        if 'reward' in metrics:
            fig.add_trace(
                go.Scatter(x=episodes, y=metrics['reward'], 
                          name='Reward', line=dict(color='blue')),
                row=1, col=1
            )
        
        # Safety violations
        if 'safety_violations' in metrics:
            fig.add_trace(
                go.Scatter(x=episodes, y=metrics['safety_violations'],
                          name='Violations', line=dict(color='red')),
                row=1, col=2
            )
        
        # Episode length
        if 'episode_length' in metrics:
            fig.add_trace(
                go.Scatter(x=episodes, y=metrics['episode_length'],
                          name='Length', line=dict(color='green')),
                row=2, col=1
            )
        
        # Violation rate
        if 'violation_rate' in metrics:
            fig.add_trace(
                go.Scatter(x=episodes, y=metrics['violation_rate'],
                          name='Rate', line=dict(color='orange')),
                row=2, col=2
            )
        
        fig.update_layout(
            title_text="Meta-Reward Safe RL Dashboard",
            showlegend=False,
            height=600
        )
        
        return fig
    
    def compare_experiments(self, experiments: Dict[str, Dict[str, List]],
                          metric: str = 'reward', window_size: int = 100,
                          save_name: str = "experiment_comparison.png") -> plt.Figure:
        """Compare multiple experiments."""
        fig, ax = plt.subplots(figsize=(12, 8))
        
        colors = plt.cm.Set1(np.linspace(0, 1, len(experiments)))
        
        for i, (exp_name, metrics) in enumerate(experiments.items()):
            if metric in metrics and metrics[metric]:
                episodes = metrics.get('episode', range(len(metrics[metric])))
                values = metrics[metric]
                smoothed_values = self._moving_average(values, window_size)
                
                ax.plot(episodes, values, alpha=0.3, color=colors[i])
                ax.plot(episodes, smoothed_values, color=colors[i], 
                       linewidth=2, label=exp_name)
        
        ax.set_title(f'Experiment Comparison: {metric.replace("_", " ").title()}')
        ax.set_xlabel('Episode')
        ax.set_ylabel(metric.replace("_", " ").title())
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save plot
        save_path = os.path.join(self.save_dir, save_name)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def _moving_average(self, data: List[float], window_size: int) -> List[float]:
        """Compute moving average."""
        if len(data) < window_size:
            return data
        
        smoothed = []
        for i in range(len(data)):
            start_idx = max(0, i - window_size + 1)
            smoothed.append(np.mean(data[start_idx:i+1]))
        
        return smoothed
    
    def save_all_plots(self, metrics: Dict[str, List], prefix: str = ""):
        """Save all standard plots."""
        if prefix:
            prefix += "_"
        
        self.plot_learning_curves(metrics, save_name=f"{prefix}learning_curves.png")
        self.plot_training_losses(metrics, save_name=f"{prefix}training_losses.png")
        self.plot_safety_analysis(metrics, save_name=f"{prefix}safety_analysis.png")
