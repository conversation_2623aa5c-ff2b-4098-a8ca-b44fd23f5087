# Baseline PPO configuration (no meta-learning)

experiment:
  name: "baseline_ppo_cartpole"
  seed: 42
  total_episodes: 2000
  max_steps_per_episode: 500
  eval_frequency: 100
  save_frequency: 500

environment:
  name: "SafeCartPole"
  unsafe_angle_threshold: 20.0  # degrees
  unsafe_position_threshold: 1.5
  unsafe_velocity_threshold: 3.0
  safety_penalty: -10.0

agent:
  type: "PPO"
  lr: 0.0003
  gamma: 0.99
  eps_clip: 0.2
  k_epochs: 4
  hidden_dim: 64
  batch_size: 64
  trajectory_length: 2048

meta_learner:
  enabled: false

safety_critic:
  enabled: false

logging:
  log_dir: "logs"
  save_frequency: 100
  plot_frequency: 500

device: "cpu"
