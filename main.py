#!/usr/bin/env python3
"""
Meta-Reward Design for Safe Exploration in Reinforcement Learning
Main entry point for the application.
"""

import argparse
import os
import sys
from pathlib import Path

def launch_web_ui():
    """Launch the Streamlit web interface."""
    import subprocess
    ui_path = Path(__file__).parent / "ui" / "app.py"
    subprocess.run([sys.executable, "-m", "streamlit", "run", str(ui_path)])

def run_experiment(config_path: str):
    """Run a training experiment with the given configuration."""
    from experiments.train import run_training
    run_training(config_path)

def main():
    parser = argparse.ArgumentParser(description="Meta-Reward Safe RL System")
    parser.add_argument("--mode", choices=["ui", "train"], default="ui",
                       help="Run mode: 'ui' for web interface, 'train' for CLI training")
    parser.add_argument("--config", type=str, default="experiments/configs/base_cartpole.yml",
                       help="Configuration file for training")
    
    args = parser.parse_args()
    
    if args.mode == "ui":
        print("Launching web interface...")
        launch_web_ui()
    elif args.mode == "train":
        print(f"Running training with config: {args.config}")
        run_experiment(args.config)

if __name__ == "__main__":
    main()
