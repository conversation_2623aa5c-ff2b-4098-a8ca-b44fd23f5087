"""
Safe CartPole environment with unsafe regions.
Modified CartPole-v1 with additional safety constraints.
"""

import gymnasium as gym
import numpy as np
from typing import Tuple, Dict, Any, Optional
import math


class SafeCartPoleEnv(gym.Wrapper):
    """
    CartPole environment with safety constraints.
    
    Unsafe regions:
    1. Extreme pole angles (beyond ±20 degrees)
    2. Extreme cart positions (beyond ±1.5)
    3. High velocities that could lead to unsafe states
    """
    
    def __init__(self, env_id: str = "CartPole-v1", 
                 unsafe_angle_threshold: float = 20.0,  # degrees
                 unsafe_position_threshold: float = 1.5,
                 unsafe_velocity_threshold: float = 3.0,
                 safety_penalty: float = -10.0):
        
        # Create base environment
        base_env = gym.make(env_id)
        super().__init__(base_env)
        
        # Safety parameters
        self.unsafe_angle_threshold = math.radians(unsafe_angle_threshold)
        self.unsafe_position_threshold = unsafe_position_threshold
        self.unsafe_velocity_threshold = unsafe_velocity_threshold
        self.safety_penalty = safety_penalty
        
        # Safety tracking
        self.safety_violations = 0
        self.total_steps = 0
        self.episode_violations = 0
        
        # Add safety info to observation space metadata
        self.observation_space = base_env.observation_space
        self.action_space = base_env.action_space
        
    def reset(self, **kwargs) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Reset environment and safety tracking."""
        obs, info = self.env.reset(**kwargs)
        self.episode_violations = 0
        
        # Add safety info
        info.update({
            'safety_violation': 0.0,
            'is_unsafe': False,
            'safety_info': self._get_safety_info(obs)
        })
        
        return obs, info
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, bool, Dict[str, Any]]:
        """Step with safety monitoring."""
        obs, reward, terminated, truncated, info = self.env.step(action)
        self.total_steps += 1
        
        # Check for safety violations
        violation, is_unsafe, safety_info = self._check_safety_violation(obs)
        
        if violation > 0:
            self.safety_violations += 1
            self.episode_violations += 1
            # Apply safety penalty to reward
            reward += self.safety_penalty * violation
        
        # Add safety information to info dict
        info.update({
            'safety_violation': violation,
            'is_unsafe': is_unsafe,
            'safety_info': safety_info,
            'total_violations': self.safety_violations,
            'episode_violations': self.episode_violations,
            'violation_rate': self.safety_violations / max(1, self.total_steps)
        })
        
        return obs, reward, terminated, truncated, info
    
    def _check_safety_violation(self, obs: np.ndarray) -> Tuple[float, bool, Dict[str, Any]]:
        """
        Check if current state violates safety constraints.
        
        Returns:
            violation: Float indicating severity of violation (0.0 = safe, 1.0 = max violation)
            is_unsafe: Boolean indicating if state is unsafe
            safety_info: Dictionary with detailed safety information
        """
        cart_pos, cart_vel, pole_angle, pole_vel = obs
        
        violations = []
        safety_info = {}
        
        # Check pole angle violation
        angle_violation = 0.0
        if abs(pole_angle) > self.unsafe_angle_threshold:
            angle_violation = min(1.0, (abs(pole_angle) - self.unsafe_angle_threshold) / 
                                 (math.pi/2 - self.unsafe_angle_threshold))
            violations.append(angle_violation)
        safety_info['angle_violation'] = angle_violation
        safety_info['pole_angle_deg'] = math.degrees(pole_angle)
        
        # Check cart position violation
        pos_violation = 0.0
        if abs(cart_pos) > self.unsafe_position_threshold:
            pos_violation = min(1.0, (abs(cart_pos) - self.unsafe_position_threshold) / 
                               (2.4 - self.unsafe_position_threshold))  # 2.4 is CartPole limit
            violations.append(pos_violation)
        safety_info['position_violation'] = pos_violation
        safety_info['cart_position'] = cart_pos
        
        # Check velocity violations (predictive safety)
        vel_violation = 0.0
        total_velocity = abs(cart_vel) + abs(pole_vel)
        if total_velocity > self.unsafe_velocity_threshold:
            vel_violation = min(1.0, (total_velocity - self.unsafe_velocity_threshold) / 
                               self.unsafe_velocity_threshold)
            violations.append(vel_violation)
        safety_info['velocity_violation'] = vel_violation
        safety_info['total_velocity'] = total_velocity
        
        # Overall violation is the maximum individual violation
        overall_violation = max(violations) if violations else 0.0
        is_unsafe = overall_violation > 0.0
        
        safety_info['overall_violation'] = overall_violation
        safety_info['is_unsafe'] = is_unsafe
        
        return overall_violation, is_unsafe, safety_info
    
    def _get_safety_info(self, obs: np.ndarray) -> Dict[str, Any]:
        """Get detailed safety information for current state."""
        _, _, safety_info = self._check_safety_violation(obs)
        return safety_info
    
    def get_safety_metrics(self) -> Dict[str, float]:
        """Get comprehensive safety metrics."""
        return {
            'total_violations': self.safety_violations,
            'total_steps': self.total_steps,
            'violation_rate': self.safety_violations / max(1, self.total_steps),
            'episode_violations': self.episode_violations
        }
    
    def is_state_safe(self, obs: np.ndarray) -> bool:
        """Check if a given state is safe."""
        violation, is_unsafe, _ = self._check_safety_violation(obs)
        return not is_unsafe
    
    def get_safety_margin(self, obs: np.ndarray) -> float:
        """Get safety margin (distance to unsafe region)."""
        cart_pos, cart_vel, pole_angle, pole_vel = obs
        
        # Calculate margins for each constraint
        angle_margin = self.unsafe_angle_threshold - abs(pole_angle)
        pos_margin = self.unsafe_position_threshold - abs(cart_pos)
        vel_margin = self.unsafe_velocity_threshold - (abs(cart_vel) + abs(pole_vel))
        
        # Return minimum margin (closest to violation)
        return min(angle_margin, pos_margin, vel_margin)


def make_safe_cartpole(unsafe_angle_threshold: float = 20.0,
                      unsafe_position_threshold: float = 1.5,
                      unsafe_velocity_threshold: float = 3.0,
                      safety_penalty: float = -10.0) -> SafeCartPoleEnv:
    """Factory function to create SafeCartPole environment."""
    return SafeCartPoleEnv(
        unsafe_angle_threshold=unsafe_angle_threshold,
        unsafe_position_threshold=unsafe_position_threshold,
        unsafe_velocity_threshold=unsafe_velocity_threshold,
        safety_penalty=safety_penalty
    )


# Example usage and testing
if __name__ == "__main__":
    # Test the safe environment
    env = make_safe_cartpole()
    obs, info = env.reset()
    
    print("Initial observation:", obs)
    print("Initial safety info:", info['safety_info'])
    
    # Run a few random steps
    for i in range(10):
        action = env.action_space.sample()
        obs, reward, terminated, truncated, info = env.step(action)
        
        print(f"Step {i+1}:")
        print(f"  Action: {action}, Reward: {reward:.2f}")
        print(f"  Safety violation: {info['safety_violation']:.3f}")
        print(f"  Is unsafe: {info['is_unsafe']}")
        
        if terminated or truncated:
            print("Episode ended")
            break
    
    print("\nFinal safety metrics:", env.get_safety_metrics())
    env.close()
