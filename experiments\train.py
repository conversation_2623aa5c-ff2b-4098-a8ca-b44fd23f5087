"""
Training script for Meta-Reward Safe RL experiments.
"""

import yaml
import torch
import numpy as np
from pathlib import Path
import sys
import os

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from agents.ppo_agent import PPOAgent
from agents.meta_learner import Meta<PERSON><PERSON><PERSON>
from agents.safety_critic import SafetyCritic
from environments.cartpole_safe import make_safe_cartpole
from utils.logging import ExperimentLogger, set_random_seeds
from utils.visualization import ExperimentVisualizer


class SafeRLTrainer:
    """Main trainer for Meta-Reward Safe RL experiments."""
    
    def __init__(self, config_path: str):
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Set random seed
        set_random_seeds(self.config['experiment']['seed'])
        
        # Setup device
        self.device = self.config.get('device', 'cpu')
        if self.device == 'cuda' and not torch.cuda.is_available():
            print("CUDA not available, using CPU")
            self.device = 'cpu'
        
        # Initialize environment
        self.env = self._create_environment()
        
        # Get environment dimensions
        state_dim = self.env.observation_space.shape[0]
        # For discrete actions, we use 1 dimension for the action value
        action_dim = 1 if hasattr(self.env.action_space, 'n') else self.env.action_space.shape[0]
        
        # Initialize agents
        self.agent = self._create_agent(state_dim, action_dim)
        self.meta_learner = self._create_meta_learner(state_dim, action_dim)
        self.safety_critic = self._create_safety_critic(state_dim, action_dim)
        
        # Initialize logging
        self.logger = ExperimentLogger(
            self.config['experiment']['name'],
            self.config['logging']['log_dir'],
            self.config['logging']['save_frequency']
        )
        self.logger.save_config(self.config)
        
        # Initialize visualization
        self.visualizer = ExperimentVisualizer(
            os.path.join(self.logger.experiment_dir, "plots")
        )
        
    def _create_environment(self):
        """Create the training environment."""
        env_config = self.config['environment']
        
        if env_config['name'] == 'SafeCartPole':
            return make_safe_cartpole(
                unsafe_angle_threshold=env_config['unsafe_angle_threshold'],
                unsafe_position_threshold=env_config['unsafe_position_threshold'],
                unsafe_velocity_threshold=env_config['unsafe_velocity_threshold'],
                safety_penalty=env_config['safety_penalty']
            )
        else:
            raise ValueError(f"Unknown environment: {env_config['name']}")
    
    def _create_agent(self, state_dim: int, action_dim: int):
        """Create the PPO agent."""
        agent_config = self.config['agent']

        # For PPO agent, we need the actual number of discrete actions
        ppo_action_dim = self.env.action_space.n if hasattr(self.env.action_space, 'n') else action_dim

        return PPOAgent(
            state_dim=state_dim,
            action_dim=ppo_action_dim,
            lr=agent_config['lr'],
            gamma=agent_config['gamma'],
            eps_clip=agent_config['eps_clip'],
            k_epochs=agent_config['k_epochs'],
            continuous=False,
            device=self.device
        )
    
    def _create_meta_learner(self, state_dim: int, action_dim: int):
        """Create the meta-learner if enabled."""
        meta_config = self.config['meta_learner']
        
        if not meta_config['enabled']:
            return None
        
        return MetaLearner(
            state_dim=state_dim,
            action_dim=action_dim,
            lr=meta_config['lr'],
            history_length=meta_config['history_length'],
            shaping_scale=meta_config['shaping_scale'],
            use_lstm=meta_config['use_lstm'],
            device=self.device
        )
    
    def _create_safety_critic(self, state_dim: int, action_dim: int):
        """Create the safety critic if enabled."""
        safety_config = self.config['safety_critic']
        
        if not safety_config['enabled']:
            return None
        
        return SafetyCritic(
            state_dim=state_dim,
            action_dim=action_dim,
            lr=safety_config['lr'],
            gamma=safety_config['gamma'],
            device=self.device
        )
    
    def train(self):
        """Main training loop."""
        total_episodes = self.config['experiment']['total_episodes']
        max_steps = self.config['experiment']['max_steps_per_episode']
        eval_freq = self.config['experiment']['eval_frequency']
        save_freq = self.config['experiment']['save_frequency']
        plot_freq = self.config['logging']['plot_frequency']
        
        print(f"Starting training for {total_episodes} episodes...")
        
        for episode in range(1, total_episodes + 1):
            # Run episode
            episode_metrics = self._run_episode(max_steps)
            
            # Log episode metrics (exclude episode_data)
            log_metrics = {k: v for k, v in episode_metrics.items() if k != 'episode_data'}
            self.logger.log_episode(**log_metrics)
            
            # Update agents
            training_metrics = self._update_agents(episode_metrics)

            # Filter training metrics to only include expected parameters
            expected_metrics = ['policy_loss', 'value_loss', 'meta_loss', 'safety_critic_loss']
            filtered_metrics = {k: v for k, v in training_metrics.items() if k in expected_metrics}
            self.logger.log_training_metrics(**filtered_metrics)
            
            # Evaluation
            if episode % eval_freq == 0:
                eval_metrics = self._evaluate(num_episodes=5)
                print(f"Episode {episode} - Eval Reward: {eval_metrics['avg_reward']:.2f}, "
                      f"Eval Violations: {eval_metrics['avg_violations']:.2f}")
            
            # Save models
            if episode % save_freq == 0:
                self._save_models(episode)
            
            # Generate plots
            if episode % plot_freq == 0:
                self._generate_plots()
        
        # Final evaluation and cleanup
        print("Training completed!")
        final_eval = self._evaluate(num_episodes=10)
        print(f"Final evaluation - Reward: {final_eval['avg_reward']:.2f}, "
              f"Violations: {final_eval['avg_violations']:.2f}")
        
        self._generate_plots()
        self.logger.close()
    
    def _run_episode(self, max_steps: int) -> dict:
        """Run a single training episode."""
        obs, info = self.env.reset()
        
        episode_reward = 0
        episode_length = 0
        episode_violations = 0
        episode_meta_rewards = []
        episode_safety_costs = []
        
        # Reset meta-learner safety history
        if self.meta_learner:
            self.meta_learner.reset_safety_history()
        
        episode_data = {
            'states': [],
            'actions': [],
            'rewards': [],
            'violations': [],
            'meta_rewards': []
        }
        
        for step in range(max_steps):
            # Get action from agent
            action, log_prob, value = self.agent.get_action(obs)
            action_discrete = int(action[0])
            
            # Get meta-reward shaping if enabled
            meta_reward = 0.0
            if self.meta_learner:
                meta_reward = self.meta_learner.get_shaping_reward(obs, action)
            
            # Take environment step
            next_obs, env_reward, terminated, truncated, info = self.env.step(action_discrete)
            
            # Extract safety information
            safety_violation = info.get('safety_violation', 0.0)
            
            # Update meta-learner safety history
            if self.meta_learner:
                self.meta_learner.update_safety_history(safety_violation)
            
            # Compute total reward (environment + meta-reward)
            total_reward = env_reward + meta_reward
            
            # Store transition in agent
            self.agent.store_transition(obs, action, log_prob, total_reward, value, terminated or truncated)
            
            # Store experience for meta-learner
            if self.meta_learner:
                self.meta_learner.store_experience(obs, action, env_reward)
            
            # Store transition for safety critic
            if self.safety_critic and step > 0:  # Need next action for safety critic
                next_action, _, _ = self.agent.get_action(next_obs)
                self.safety_critic.store_transition(
                    prev_obs, prev_action, safety_violation, obs, action, terminated or truncated
                )
            
            # Store episode data
            episode_data['states'].append(obs)
            episode_data['actions'].append(action)
            episode_data['rewards'].append(env_reward)
            episode_data['violations'].append(safety_violation)
            episode_data['meta_rewards'].append(meta_reward)
            
            # Update metrics
            episode_reward += env_reward
            episode_length += 1
            if safety_violation > 0:
                episode_violations += 1
            episode_meta_rewards.append(meta_reward)
            episode_safety_costs.append(safety_violation)
            
            # Store for next iteration
            prev_obs, prev_action = obs, action
            obs = next_obs
            
            if terminated or truncated:
                break
        
        # Compute episode metrics
        violation_rate = episode_violations / max(1, episode_length)
        avg_meta_reward = np.mean(episode_meta_rewards) if episode_meta_rewards else 0.0
        avg_safety_cost = np.mean(episode_safety_costs) if episode_safety_costs else 0.0
        
        return {
            'episode_reward': episode_reward,
            'episode_length': episode_length,
            'safety_violations': episode_violations,
            'violation_rate': violation_rate,
            'meta_reward': avg_meta_reward,
            'safety_cost': avg_safety_cost,
            'episode_data': episode_data
        }
    
    def _update_agents(self, episode_metrics: dict) -> dict:
        """Update all agents based on episode data."""
        training_metrics = {}
        
        # Update PPO agent
        if len(self.agent.states) > 0:
            ppo_metrics = self.agent.update()
            training_metrics.update(ppo_metrics)
        
        # Update meta-learner
        if self.meta_learner:
            episode_data = episode_metrics['episode_data']
            meta_metrics = self.meta_learner.update(
                episode_data['rewards'],
                episode_data['violations'],
                self.config['meta_learner']['safety_weight']
            )
            training_metrics.update(meta_metrics)
        
        # Update safety critic
        if self.safety_critic and len(self.safety_critic.states) > 0:
            safety_metrics = self.safety_critic.update_supervised(
                self.config['safety_critic']['batch_size']
            )
            training_metrics.update(safety_metrics)
            
            # Clean up old experiences
            self.safety_critic.clear_old_experiences()
        
        return training_metrics
    
    def _evaluate(self, num_episodes: int = 5) -> dict:
        """Evaluate the current policy."""
        total_reward = 0
        total_violations = 0
        total_length = 0
        
        for _ in range(num_episodes):
            obs, _ = self.env.reset()
            episode_reward = 0
            episode_violations = 0
            episode_length = 0
            
            for step in range(self.config['experiment']['max_steps_per_episode']):
                action, _, _ = self.agent.get_action(obs)
                action_discrete = int(action[0])
                
                obs, reward, terminated, truncated, info = self.env.step(action_discrete)
                
                episode_reward += reward
                episode_length += 1
                if info.get('safety_violation', 0.0) > 0:
                    episode_violations += 1
                
                if terminated or truncated:
                    break
            
            total_reward += episode_reward
            total_violations += episode_violations
            total_length += episode_length
        
        return {
            'avg_reward': total_reward / num_episodes,
            'avg_violations': total_violations / num_episodes,
            'avg_length': total_length / num_episodes
        }
    
    def _save_models(self, episode: int):
        """Save model checkpoints."""
        models_dir = self.logger.experiment_dir / "models"
        models_dir.mkdir(exist_ok=True)
        
        # Save PPO agent
        self.agent.save(models_dir / f"ppo_episode_{episode}.pt")
        
        # Save meta-learner
        if self.meta_learner:
            self.meta_learner.save(models_dir / f"meta_learner_episode_{episode}.pt")
        
        # Save safety critic
        if self.safety_critic:
            self.safety_critic.save(models_dir / f"safety_critic_episode_{episode}.pt")
    
    def _generate_plots(self):
        """Generate visualization plots."""
        metrics = self.logger.metrics
        if metrics['episode']:
            self.visualizer.save_all_plots(metrics)


def run_training(config_path: str):
    """Run training with given configuration."""
    trainer = SafeRLTrainer(config_path)
    trainer.train()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Train Meta-Reward Safe RL")
    parser.add_argument("--config", type=str, 
                       default="experiments/configs/base_cartpole.yml",
                       help="Configuration file path")
    
    args = parser.parse_args()
    run_training(args.config)
