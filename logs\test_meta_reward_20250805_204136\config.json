{"experiment": {"name": "test_meta_reward", "seed": 42, "total_episodes": 10, "max_steps_per_episode": 100, "eval_frequency": 5, "save_frequency": 10}, "environment": {"name": "SafeCartPole", "unsafe_angle_threshold": 20.0, "unsafe_position_threshold": 1.5, "unsafe_velocity_threshold": 3.0, "safety_penalty": -10.0}, "agent": {"type": "PPO", "lr": 0.0003, "gamma": 0.99, "eps_clip": 0.2, "k_epochs": 2, "hidden_dim": 32, "batch_size": 32, "trajectory_length": 128}, "meta_learner": {"enabled": true, "lr": 0.001, "history_length": 5, "shaping_scale": 0.1, "use_lstm": false, "hidden_dim": 32, "safety_weight": 1.0}, "safety_critic": {"enabled": true, "lr": 0.001, "gamma": 0.99, "hidden_dim": 32, "batch_size": 16, "update_frequency": 5}, "logging": {"log_dir": "logs", "save_frequency": 5, "plot_frequency": 10}, "device": "cpu"}