# Base configuration for CartPole Safe RL experiments

experiment:
  name: "meta_reward_safe_cartpole"
  seed: 42
  total_episodes: 2000
  max_steps_per_episode: 500
  eval_frequency: 100
  save_frequency: 500

environment:
  name: "SafeCartPole"
  unsafe_angle_threshold: 20.0  # degrees
  unsafe_position_threshold: 1.5
  unsafe_velocity_threshold: 3.0
  safety_penalty: -10.0

agent:
  type: "PPO"
  lr: 0.0003
  gamma: 0.99
  eps_clip: 0.2
  k_epochs: 4
  hidden_dim: 64
  batch_size: 64
  trajectory_length: 2048

meta_learner:
  enabled: true
  lr: 0.001
  history_length: 10
  shaping_scale: 0.1
  use_lstm: true
  hidden_dim: 64
  safety_weight: 1.0

safety_critic:
  enabled: true
  lr: 0.001
  gamma: 0.99
  hidden_dim: 64
  batch_size: 32
  update_frequency: 10

logging:
  log_dir: "logs"
  save_frequency: 100
  plot_frequency: 500

device: "cpu"  # or "cuda" if available
