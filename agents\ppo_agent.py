"""
PPO Agent implementation using PyTorch.
Base learner for the meta-reward safe exploration system.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Categorical, Normal
import numpy as np
from typing import Tuple, Dict, Any, Optional


class ActorCritic(nn.Module):
    """Actor-Critic network for PPO."""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 64, 
                 continuous: bool = False):
        super(ActorCritic, self).__init__()
        self.continuous = continuous
        
        # Shared feature extractor
        self.shared_net = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU()
        )
        
        # Actor head
        if continuous:
            self.actor_mean = nn.Linear(hidden_dim, action_dim)
            self.actor_logstd = nn.Parameter(torch.zeros(action_dim))
        else:
            self.actor = nn.Linear(hidden_dim, action_dim)
        
        # Critic head
        self.critic = nn.Linear(hidden_dim, 1)
        
    def forward(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass returning action distribution and value."""
        features = self.shared_net(state)
        
        if self.continuous:
            mean = self.actor_mean(features)
            std = torch.exp(self.actor_logstd)
            dist = Normal(mean, std)
        else:
            logits = self.actor(features)
            dist = Categorical(logits=logits)
        
        value = self.critic(features)
        return dist, value
    
    def get_action(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Get action, log probability, and value for given state."""
        dist, value = self.forward(state)
        action = dist.sample()
        log_prob = dist.log_prob(action)
        
        if not self.continuous:
            log_prob = log_prob.unsqueeze(-1)
        
        return action, log_prob, value


class PPOAgent:
    """Proximal Policy Optimization agent."""
    
    def __init__(self, state_dim: int, action_dim: int, lr: float = 3e-4,
                 gamma: float = 0.99, eps_clip: float = 0.2, k_epochs: int = 4,
                 continuous: bool = False, device: str = "cpu"):
        self.device = device
        self.gamma = gamma
        self.eps_clip = eps_clip
        self.k_epochs = k_epochs
        
        # Initialize network
        self.policy = ActorCritic(state_dim, action_dim, continuous=continuous).to(device)
        self.optimizer = optim.Adam(self.policy.parameters(), lr=lr)
        
        # Storage for trajectory data
        self.reset_storage()
        
    def reset_storage(self):
        """Reset trajectory storage."""
        self.states = []
        self.actions = []
        self.log_probs = []
        self.rewards = []
        self.values = []
        self.dones = []
        
    def store_transition(self, state: np.ndarray, action: np.ndarray, 
                        log_prob: float, reward: float, value: float, done: bool):
        """Store a transition in the trajectory buffer."""
        self.states.append(state)
        self.actions.append(action)
        self.log_probs.append(log_prob)
        self.rewards.append(reward)
        self.values.append(value)
        self.dones.append(done)
        
    def get_action(self, state: np.ndarray) -> Tuple[np.ndarray, float, float]:
        """Get action for given state."""
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            action, log_prob, value = self.policy.get_action(state_tensor)
            
        return action.cpu().numpy().flatten(), log_prob.item(), value.item()
    
    def compute_returns(self, next_value: float = 0.0) -> torch.Tensor:
        """Compute discounted returns using GAE."""
        returns = []
        gae = 0
        
        for i in reversed(range(len(self.rewards))):
            if i == len(self.rewards) - 1:
                next_val = next_value
            else:
                next_val = self.values[i + 1]
                
            delta = self.rewards[i] + self.gamma * next_val * (1 - self.dones[i]) - self.values[i]
            gae = delta + self.gamma * 0.95 * (1 - self.dones[i]) * gae  # GAE with lambda=0.95
            returns.insert(0, gae + self.values[i])
            
        return torch.FloatTensor(returns).to(self.device)
    
    def update(self, next_value: float = 0.0) -> Dict[str, float]:
        """Update the policy using PPO."""
        if len(self.states) == 0:
            return {}
            
        # Convert lists to tensors
        states = torch.FloatTensor(np.array(self.states)).to(self.device)
        actions = torch.FloatTensor(np.array(self.actions)).to(self.device)
        old_log_probs = torch.FloatTensor(self.log_probs).to(self.device)
        old_values = torch.FloatTensor(self.values).to(self.device)
        
        # Compute returns and advantages
        returns = self.compute_returns(next_value)
        advantages = returns - old_values
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # PPO update
        total_policy_loss = 0
        total_value_loss = 0
        
        for _ in range(self.k_epochs):
            # Get current policy outputs
            dist, values = self.policy(states)
            
            if self.policy.continuous:
                new_log_probs = dist.log_prob(actions).sum(dim=-1, keepdim=True)
            else:
                new_log_probs = dist.log_prob(actions.squeeze().long()).unsqueeze(-1)
            
            # Compute ratio and surrogate loss
            ratio = torch.exp(new_log_probs - old_log_probs)
            surr1 = ratio * advantages
            surr2 = torch.clamp(ratio, 1 - self.eps_clip, 1 + self.eps_clip) * advantages
            policy_loss = -torch.min(surr1, surr2).mean()
            
            # Value loss
            value_loss = F.mse_loss(values.squeeze(), returns)
            
            # Total loss
            loss = policy_loss + 0.5 * value_loss
            
            # Update
            self.optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.policy.parameters(), 0.5)
            self.optimizer.step()
            
            total_policy_loss += policy_loss.item()
            total_value_loss += value_loss.item()
        
        # Clear storage
        self.reset_storage()
        
        return {
            'policy_loss': total_policy_loss / self.k_epochs,
            'value_loss': total_value_loss / self.k_epochs
        }
    
    def save(self, filepath: str):
        """Save the model."""
        torch.save({
            'policy_state_dict': self.policy.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict()
        }, filepath)
    
    def load(self, filepath: str):
        """Load the model."""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.policy.load_state_dict(checkpoint['policy_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
