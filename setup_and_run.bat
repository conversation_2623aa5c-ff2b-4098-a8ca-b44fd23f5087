@echo off
REM Meta-Reward Safe RL Setup and Run Script for Windows
REM This script helps Windows users set up and run the project easily

echo ========================================
echo Meta-Reward Safe RL Setup Script
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo Python found:
python --version
echo.

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment
        pause
        exit /b 1
    )
    echo Virtual environment created successfully.
    echo.
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Dependencies installed successfully.
echo.

REM Run installation test
echo Running installation test...
python test_installation.py
if errorlevel 1 (
    echo.
    echo WARNING: Some tests failed. The installation might not be complete.
    echo You can still try to run the application.
    echo.
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Choose an option:
echo 1. Launch Web Interface (Recommended)
echo 2. Run Command Line Training
echo 3. Exit
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo Launching web interface...
    echo Open your browser to http://localhost:8501
    echo Press Ctrl+C to stop the server
    echo.
    python main.py --mode ui
) else if "%choice%"=="2" (
    echo.
    echo Running command line training...
    python main.py --mode train --config experiments/configs/base_cartpole.yml
) else if "%choice%"=="3" (
    echo Goodbye!
) else (
    echo Invalid choice. Exiting.
)

echo.
pause
